'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import AdminDashboardLayout from '@/components/navigation/AdminDashboardLayout';
import withAdminAuth from '@/components/withAdminAuth';
import {
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  CameraIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';

interface AdminProfileProps {
  adminData: any;
}

interface ProfileData {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  username: string;
  full_name: string;
  admin_role: string;
  profile_picture: string | null;
  created_at: string;
  updated_at: string;
}

function AdminProfilePage({ adminData }: AdminProfileProps) {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingPicture, setIsUploadingPicture] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    current_password: '',
    new_password: '',
    confirm_password: ''
  });

  // Load profile data on component mount
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        const response = await fetch('/api/admin/profile');
        if (response.ok) {
          const data = await response.json();
          setProfileData(data.profile);
          setFormData({
            first_name: data.profile.first_name,
            last_name: data.profile.last_name,
            email: data.profile.email,
            current_password: '',
            new_password: '',
            confirm_password: ''
          });
        } else {
          setMessage({ text: 'Failed to load profile data', type: 'error' });
        }
      } catch (error) {
        console.error('Failed to load profile data:', error);
        setMessage({ text: 'Failed to load profile data', type: 'error' });
      } finally {
        setIsLoading(false);
      }
    };

    loadProfileData();
  }, []);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle profile picture upload
  const handleProfilePictureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploadingPicture(true);
    setMessage(null);

    try {
      const formData = new FormData();
      formData.append('profilePicture', file);
      formData.append('userId', adminData.id.toString());

      const response = await fetch('/api/admin/upload-profile-picture', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setProfileData(prev => prev ? { ...prev, profile_picture: data.profilePicturePath } : null);

        // Update admin data in session storage
        const adminData = sessionStorage.getItem('admin_data');
        if (adminData) {
          try {
            const admin = JSON.parse(adminData);
            admin.profile_picture = data.profilePicturePath;
            sessionStorage.setItem('admin_data', JSON.stringify(admin));

            // Trigger profile picture update event
            window.dispatchEvent(new Event('profilePictureUpdated'));
          } catch (error) {
            console.error('Failed to update admin data in session storage:', error);
          }
        }

        setMessage({ text: 'Profile picture updated successfully!', type: 'success' });
      } else {
        const errorData = await response.json();
        setMessage({ text: errorData.error || 'Failed to upload profile picture', type: 'error' });
      }
    } catch (error) {
      setMessage({ text: 'Failed to upload profile picture. Please try again.', type: 'error' });
    } finally {
      setIsUploadingPicture(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage(null);

    // Validate password confirmation if new password is provided
    if (formData.new_password && formData.new_password !== formData.confirm_password) {
      setMessage({ text: 'New passwords do not match', type: 'error' });
      setIsSaving(false);
      return;
    }

    try {
      const submitData: any = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email
      };

      // Only include password fields if new password is provided
      if (formData.new_password) {
        submitData.current_password = formData.current_password;
        submitData.new_password = formData.new_password;
      }

      const response = await fetch('/api/admin/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        setMessage({ text: 'Profile updated successfully!', type: 'success' });
        // Clear password fields
        setFormData(prev => ({
          ...prev,
          current_password: '',
          new_password: '',
          confirm_password: ''
        }));
        // Reload profile data
        const profileResponse = await fetch('/api/admin/profile');
        if (profileResponse.ok) {
          const data = await profileResponse.json();
          setProfileData(data.profile);
        }
      } else {
        const errorData = await response.json();
        setMessage({ text: errorData.error || 'Failed to update profile', type: 'error' });
      }
    } catch (error) {
      setMessage({ text: 'Failed to update profile. Please try again.', type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  const userName = profileData ? `${profileData.first_name} ${profileData.last_name}` : 'Admin';

  return (
    <AdminDashboardLayout activePage="profile" userName={userName}>
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="bg-[var(--primary-green)] rounded-full p-3 mr-4">
                <UserIcon className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-[var(--primary-green)] mb-2">Admin Profile</h1>
                <p className="text-gray-600">Manage your account information and settings</p>
              </div>
            </div>
          </div>

          {/* Message Display */}
          {message && (
            <div className={`mb-6 p-4 rounded-md flex items-center ${
              message.type === 'success'
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message.type === 'success' ? (
                <CheckCircleIcon className="h-5 w-5 mr-2" />
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          )}

          {isLoading ? (
            <div className="bg-white rounded-xl shadow-sm p-8">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Profile Picture Section */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h2 className="text-lg font-medium text-gray-800 mb-4">Profile Picture</h2>
                  <div className="flex flex-col items-center">
                    <div className="relative mb-4">
                      {profileData?.profile_picture ? (
                        <Image
                          src={profileData.profile_picture}
                          alt="Profile"
                          width={120}
                          height={120}
                          className="w-30 h-30 rounded-full object-cover border-4 border-gray-200"
                        />
                      ) : (
                        <div className="w-30 h-30 rounded-full bg-gray-200 flex items-center justify-center border-4 border-gray-200">
                          <UserIcon className="h-16 w-16 text-gray-400" />
                        </div>
                      )}
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploadingPicture}
                        className="absolute bottom-0 right-0 bg-[var(--primary-green)] text-white p-2 rounded-full hover:bg-[var(--primary-green-hover)] transition-colors disabled:opacity-50"
                      >
                        <CameraIcon className="h-4 w-4" />
                      </button>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleProfilePictureUpload}
                      className="hidden"
                    />
                    {isUploadingPicture && (
                      <p className="text-sm text-gray-500">Uploading...</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Profile Information Form */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h2 className="text-lg font-medium text-gray-800 mb-6">Account Information</h2>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-2">
                          First Name
                        </label>
                        <input
                          type="text"
                          id="first_name"
                          name="first_name"
                          value={formData.first_name}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-2">
                          Last Name
                        </label>
                        <input
                          type="text"
                          id="last_name"
                          name="last_name"
                          value={formData.last_name}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                      />
                    </div>

                    {/* Password Change Section */}
                    <div className="border-t border-gray-200 pt-6">
                      <h3 className="text-md font-medium text-gray-800 mb-4">Change Password (Optional)</h3>
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="current_password" className="block text-sm font-medium text-gray-700 mb-2">
                            Current Password
                          </label>
                          <input
                            type="password"
                            id="current_password"
                            name="current_password"
                            value={formData.current_password}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="new_password" className="block text-sm font-medium text-gray-700 mb-2">
                              New Password
                            </label>
                            <input
                              type="password"
                              id="new_password"
                              name="new_password"
                              value={formData.new_password}
                              onChange={handleInputChange}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700 mb-2">
                              Confirm New Password
                            </label>
                            <input
                              type="password"
                              id="confirm_password"
                              name="confirm_password"
                              value={formData.confirm_password}
                              onChange={handleInputChange}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-6 border-t border-gray-200">
                      <button
                        type="submit"
                        disabled={isSaving}
                        className="bg-[var(--primary-green)] text-white px-6 py-2 rounded-md font-medium hover:bg-[var(--primary-green-hover)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                      >
                        {isSaving ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Saving...
                          </>
                        ) : (
                          'Save Changes'
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </AdminDashboardLayout>
  );
}

export default withAdminAuth(AdminProfilePage);
